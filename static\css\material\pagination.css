/* Material Design M2 Data Table Pagination Styles */

.mdc-data-table__pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 16px;
    min-height: 52px;
    border-top: 1px solid rgba(0, 0, 0, 0.12);
    background-color: var(--mdc-theme-surface, #fff);
    font-family: var(--mdc-typography-body2-font-family, Roboto, sans-serif);
    font-size: var(--mdc-typography-body2-font-size, 0.875rem);
    line-height: var(--mdc-typography-body2-line-height, 1.25rem);
    letter-spacing: var(--mdc-typography-body2-letter-spacing, 0.0178571429em);
}

.mdc-data-table__pagination-trailing {
    display: flex;
    align-items: center;
    gap: 32px;
}

/* Rows per page selector */
.mdc-data-table__pagination-rows-per-page {
    display: flex;
    align-items: center;
    gap: 8px;
}

.mdc-data-table__pagination-rows-per-page-label {
    color: var(--mdc-theme-on-surface, rgba(0, 0, 0, 0.87));
    white-space: nowrap;
}

.mdc-data-table__pagination-rows-per-page-select {
    min-width: 80px;
}

.mdc-data-table__pagination-rows-per-page-select .mdc-select__anchor {
    height: 36px;
    min-height: 36px;
}

.mdc-data-table__pagination-rows-per-page-select .mdc-select__selected-text {
    font-size: 0.875rem;
}

/* Pagination navigation */
.mdc-data-table__pagination-navigation {
    display: flex;
    align-items: center;
    gap: 24px;
}

.mdc-data-table__pagination-total {
    color: var(--mdc-theme-on-surface, rgba(0, 0, 0, 0.87));
    white-space: nowrap;
}

/* Pagination buttons container */
.mdc-data-table__pagination-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Pagination icon buttons */
.mdc-data-table__pagination-button {
    width: 40px;
    height: 40px;
    padding: 8px;
    color: var(--mdc-theme-on-surface, rgba(0, 0, 0, 0.87));
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.mdc-data-table__pagination-button:hover:not(:disabled) {
    background-color: var(--mdc-theme-primary, #6200ee);
    background-color: rgba(var(--mdc-theme-primary-rgb, 98, 0, 238), 0.04);
}

.mdc-data-table__pagination-button:focus:not(:disabled) {
    background-color: rgba(var(--mdc-theme-primary-rgb, 98, 0, 238), 0.12);
}

.mdc-data-table__pagination-button:active:not(:disabled) {
    background-color: rgba(var(--mdc-theme-primary-rgb, 98, 0, 238), 0.16);
}

.mdc-data-table__pagination-button:disabled {
    color: rgba(0, 0, 0, 0.38);
    cursor: default;
}

.mdc-data-table__pagination-button .material-icons {
    font-size: 24px;
}

/* Page numbers container */
.mdc-data-table__pagination-page-numbers {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Page number buttons */
.mdc-data-table__pagination-page-button {
    min-width: 40px;
    height: 36px;
    padding: 0 8px;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 4px;
    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.mdc-data-table__pagination-page-button.mdc-button--outlined {
    border-color: rgba(0, 0, 0, 0.12);
    color: var(--mdc-theme-primary, #6200ee);
}

.mdc-data-table__pagination-page-button.mdc-button--raised {
    background-color: var(--mdc-theme-primary, #6200ee);
    color: var(--mdc-theme-on-primary, #fff);
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2),
                0px 2px 2px 0px rgba(0, 0, 0, 0.14),
                0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.mdc-data-table__pagination-page-button:disabled {
    background-color: var(--mdc-theme-primary, #6200ee);
    color: var(--mdc-theme-on-primary, #fff);
    cursor: default;
}

/* Ellipsis */
.mdc-data-table__pagination-ellipsis {
    color: rgba(0, 0, 0, 0.6);
    padding: 0 8px;
    font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .mdc-data-table__pagination {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
        padding: 16px;
        min-height: auto;
    }

    .mdc-data-table__pagination-trailing {
        flex-direction: column;
        gap: 16px;
    }

    .mdc-data-table__pagination-rows-per-page {
        justify-content: space-between;
        width: 100%;
    }

    .mdc-data-table__pagination-navigation {
        flex-direction: column;
        gap: 16px;
    }

    .mdc-data-table__pagination-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .mdc-data-table__pagination-page-numbers {
        justify-content: center;
        flex-wrap: wrap;
    }

    .mdc-data-table__pagination-total {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .mdc-data-table__pagination-page-numbers {
        display: none;
    }

    .mdc-data-table__pagination-buttons {
        gap: 16px;
    }

    .mdc-data-table__pagination-button {
        width: 48px;
        height: 48px;
    }
}

/* Dark theme support */
[data-theme="dark"] .mdc-data-table__pagination {
    background-color: var(--mdc-theme-surface, #121212);
    border-top-color: rgba(255, 255, 255, 0.12);
}

[data-theme="dark"] .mdc-data-table__pagination-rows-per-page-label,
[data-theme="dark"] .mdc-data-table__pagination-total {
    color: var(--mdc-theme-on-surface, rgba(255, 255, 255, 0.87));
}

[data-theme="dark"] .mdc-data-table__pagination-button {
    color: var(--mdc-theme-on-surface, rgba(255, 255, 255, 0.87));
}

[data-theme="dark"] .mdc-data-table__pagination-button:disabled {
    color: rgba(255, 255, 255, 0.38);
}

[data-theme="dark"] .mdc-data-table__pagination-ellipsis {
    color: rgba(255, 255, 255, 0.6);
}

/* Custom color scheme support */
.mdc-data-table__pagination {
    --pagination-primary-color: var(--app-primary-color, #4caf50);
    --pagination-primary-rgb: var(--app-primary-rgb, 76, 175, 80);
}

.mdc-data-table__pagination-button:hover:not(:disabled) {
    background-color: rgba(var(--pagination-primary-rgb), 0.04);
}

.mdc-data-table__pagination-button:focus:not(:disabled) {
    background-color: rgba(var(--pagination-primary-rgb), 0.12);
}

.mdc-data-table__pagination-button:active:not(:disabled) {
    background-color: rgba(var(--pagination-primary-rgb), 0.16);
}

.mdc-data-table__pagination-page-button.mdc-button--outlined {
    color: var(--pagination-primary-color);
}

.mdc-data-table__pagination-page-button.mdc-button--raised {
    background-color: var(--pagination-primary-color);
}

.mdc-data-table__pagination-page-button:disabled {
    background-color: var(--pagination-primary-color);
}

/* Loading state */
.mdc-data-table__pagination--loading {
    opacity: 0.6;
    pointer-events: none;
}

.mdc-data-table__pagination--loading .mdc-data-table__pagination-button,
.mdc-data-table__pagination--loading .mdc-data-table__pagination-page-button {
    cursor: wait;
}

/* Animation for smooth transitions */
.mdc-data-table__pagination-page-button {
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.mdc-data-table__pagination-button {
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus indicators for accessibility */
.mdc-data-table__pagination-button:focus-visible,
.mdc-data-table__pagination-page-button:focus-visible {
    outline: 2px solid var(--pagination-primary-color);
    outline-offset: 2px;
}
