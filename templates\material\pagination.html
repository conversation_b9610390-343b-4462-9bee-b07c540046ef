{% comment %}
Material Design M2 Data Table Pagination Component
Compatible with Django pagination and HTMX

Usage:
{% include 'material/pagination.html' with page_obj=page_obj per_page=per_page %}

Optional parameters:
- include_items: HTMX include selector for form fields
- hx_vals: Additional HTMX values to include
- target: HTMX target (default: #content-area)
- per_page_options: List of per-page options (default: [10, 25, 50, 100])
{% endcomment %}

{% load static %}

{% if page_obj %}
<div class="mdc-data-table__pagination">
    <div class="mdc-data-table__pagination-trailing">
        <!-- Rows per page selector -->
        <div class="mdc-data-table__pagination-rows-per-page">
            <div class="mdc-data-table__pagination-rows-per-page-label">
                Lignes par page:
            </div>
            <div class="mdc-select mdc-select--outlined mdc-data-table__pagination-rows-per-page-select" id="pagination-per-page-select">
                <div class="mdc-select__anchor" role="button" aria-haspopup="listbox" aria-expanded="false">
                    <span class="mdc-select__selected-text-container">
                        <span class="mdc-select__selected-text">{{ per_page|default:"10" }}</span>
                    </span>
                    <span class="mdc-select__dropdown-icon">
                        <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
                            <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                            <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                        </svg>
                    </span>
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
                <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                    <ul class="mdc-deprecated-list" role="listbox">
                        {% if per_page_options %}
                            {% for option in per_page_options %}
                            <li class="mdc-deprecated-list-item {% if option == per_page|default:"10" %}mdc-deprecated-list-item--selected{% endif %}"
                                data-value="{{ option }}"
                                role="option"
                                {% if option == per_page|default:"10" %}aria-selected="true"{% endif %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ target|default:"#content-area" }}"
                                {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                                {% if hx_vals %}
                                hx-vals='{{ hx_vals|slice:":-1" }}, "per_page": "{{ option }}", "page": "1"}'
                                {% else %}
                                hx-vals='{"per_page": "{{ option }}", "page": "1"}'
                                {% endif %}>
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">{{ option }}</span>
                            </li>
                            {% endfor %}
                        {% else %}
                            <li class="mdc-deprecated-list-item {% if "10" == per_page|default:"10" %}mdc-deprecated-list-item--selected{% endif %}"
                                data-value="10"
                                role="option"
                                {% if "10" == per_page|default:"10" %}aria-selected="true"{% endif %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ target|default:"#content-area" }}"
                                {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                                {% if hx_vals %}
                                hx-vals='{{ hx_vals|slice:":-1" }}, "per_page": "10", "page": "1"}'
                                {% else %}
                                hx-vals='{"per_page": "10", "page": "1"}'
                                {% endif %}>
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">10</span>
                            </li>
                            <li class="mdc-deprecated-list-item {% if "25" == per_page|default:"10" %}mdc-deprecated-list-item--selected{% endif %}"
                                data-value="25"
                                role="option"
                                {% if "25" == per_page|default:"10" %}aria-selected="true"{% endif %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ target|default:"#content-area" }}"
                                {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                                {% if hx_vals %}
                                hx-vals='{{ hx_vals|slice:":-1" }}, "per_page": "25", "page": "1"}'
                                {% else %}
                                hx-vals='{"per_page": "25", "page": "1"}'
                                {% endif %}>
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">25</span>
                            </li>
                            <li class="mdc-deprecated-list-item {% if "50" == per_page|default:"10" %}mdc-deprecated-list-item--selected{% endif %}"
                                data-value="50"
                                role="option"
                                {% if "50" == per_page|default:"10" %}aria-selected="true"{% endif %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ target|default:"#content-area" }}"
                                {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                                {% if hx_vals %}
                                hx-vals='{{ hx_vals|slice:":-1" }}, "per_page": "50", "page": "1"}'
                                {% else %}
                                hx-vals='{"per_page": "50", "page": "1"}'
                                {% endif %}>
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">50</span>
                            </li>
                            <li class="mdc-deprecated-list-item {% if "100" == per_page|default:"10" %}mdc-deprecated-list-item--selected{% endif %}"
                                data-value="100"
                                role="option"
                                {% if "100" == per_page|default:"10" %}aria-selected="true"{% endif %}
                                hx-get="{{ request.path }}"
                                hx-target="{{ target|default:"#content-area" }}"
                                {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                                {% if hx_vals %}
                                hx-vals='{{ hx_vals|slice:":-1" }}, "per_page": "100", "page": "1"}'
                                {% else %}
                                hx-vals='{"per_page": "100", "page": "1"}'
                                {% endif %}>
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">100</span>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>

        <!-- Pagination info -->
        <div class="mdc-data-table__pagination-navigation">
            <div class="mdc-data-table__pagination-total">
                {{ page_obj.start_index }}-{{ page_obj.end_index }} sur {{ page_obj.paginator.count }}
            </div>

            <!-- Navigation buttons container -->
            <div class="mdc-data-table__pagination-buttons">
                <!-- Left navigation buttons -->
                
                <!-- Center: Page numbers -->
                <div class="mdc-data-table__pagination-page-numbers" style="display: flex; justify-content: center; align-items: center; gap: 2px;">
                    <!-- First page button -->
                    <button class="mdc-icon-button mdc-data-table__pagination-button"
                            data-first-page="true"
                            {% if not page_obj.has_previous %}disabled{% endif %}
                            hx-get="{{ request.path }}"
                            hx-target="{{ target|default:"#content-area" }}"
                            {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                            {% if hx_vals %}
                            hx-vals='{{ hx_vals|slice:":-1" }}, "page": "1"}'
                            {% else %}
                            hx-vals='{"page": "1"}'
                            {% endif %}
                            aria-label="Première page">
                        <div class="mdc-icon-button__ripple"></div>
                        <span class="material-icons mdc-icon-button__icon">first_page</span>
                    </button>
    
                    <!-- Previous page button -->
                    <button class="mdc-icon-button mdc-data-table__pagination-button"
                            data-prev-page="true"
                            {% if not page_obj.has_previous %}disabled{% endif %}
                            hx-get="{{ request.path }}"
                            hx-target="{{ target|default:"#content-area" }}"
                            {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                            {% if hx_vals %}
                            hx-vals='{{ hx_vals|slice:":-1" }}, "page": "{{ page_obj.previous_page_number|default:"1" }}"}'
                            {% else %}
                            hx-vals='{"page": "{% if page_obj.has_previous %} {{ page_obj.previous_page_number|default:"1" }} {% endif %}"}'
                            {% endif %}
                            aria-label="Page précédente">
                        <div class="mdc-icon-button__ripple"></div>
                        <span class="material-icons mdc-icon-button__icon">chevron_left</span>
                    </button>
                    {% for page_num in page_obj.paginator.page_range %}
                        {% if page_num == 1 or page_num == page_obj.paginator.num_pages or page_num >= page_obj.number|add:"-2" and page_num <= page_obj.number|add:"2" %}
                            <button class="mdc-button mdc-data-table__pagination-page-button {% if page_num == page_obj.number %}mdc-button--raised{% else %}mdc-button--outlined{% endif %}"
                                    hx-get="{{ request.path }}"
                                    hx-target="{{ target|default:"#content-area" }}"
                                    {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                                    {% if hx_vals %}
                                    hx-vals='{{ hx_vals|slice:":-1" }}, "page": "{{ page_num }}"}'
                                    {% else %}
                                    hx-vals='{"page": "{{ page_num }}"}'
                                    {% endif %}
                                    {% if page_num == page_obj.number %}disabled{% endif %}
                                    aria-label="Page {{ page_num }}">
                                <span class="mdc-button__ripple"></span>
                                <span class="mdc-button__label">{{ page_num }}</span>
                            </button>
                        {% elif page_num == page_obj.number|add:"-3" or page_num == page_obj.number|add:"3" %}
                            <span class="mdc-data-table__pagination-ellipsis">...</span>
                        {% endif %}
                    {% endfor %}
                   <!-- Next page button -->
                    <button class="mdc-icon-button mdc-data-table__pagination-button"
                            data-next-page="true"
                            {% if not page_obj.has_next %}disabled{% endif %}
                            hx-get="{{ request.path }}"
                            hx-target="{{ target|default:"#content-area" }}"
                            {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                            {% if hx_vals %}
                            hx-vals='{{ hx_vals|slice:":-1" }}, "page": "{{ page_obj.next_page_number|default:page_obj.paginator.num_pages }}"}'
                            {% else %}
                            hx-vals='{"page": "{{ page_obj.next_page_number|default:page_obj.paginator.num_pages }}"}'
                            {% endif %}
                            aria-label="Page suivante">
                        <div class="mdc-icon-button__ripple"></div>
                        <span class="material-icons mdc-icon-button__icon">chevron_right</span>
                    </button>

                    <!-- Last page button -->
                    <button class="mdc-icon-button mdc-data-table__pagination-button"
                            data-last-page="true"
                            {% if not page_obj.has_next %}disabled{% endif %}
                            hx-get="{{ request.path }}"
                            hx-target="{{ target|default:"#content-area" }}"
                            {% if include_items %}hx-include="{{ include_items }}"{% endif %}
                            {% if hx_vals %}
                            hx-vals='{{ hx_vals|slice:":-1" }}, "page": "{{ page_obj.paginator.num_pages }}"}'
                            {% else %}
                            hx-vals='{"page": "{{ page_obj.paginator.num_pages }}"}'
                            {% endif %}
                            aria-label="Dernière page">
                        <div class="mdc-icon-button__ripple"></div>
                        <span class="material-icons mdc-icon-button__icon">last_page</span>
                    </button>
                </div>

            </div>
        </div>
    </div>
</div>

<script>
// Initialize MDC Select for pagination per-page selector
document.addEventListener('DOMContentLoaded', function() {
    const paginationSelect = document.getElementById('pagination-per-page-select');
    if (paginationSelect && typeof mdc !== 'undefined' && mdc.select) {
        const select = new mdc.select.MDCSelect(paginationSelect);
        
        // Handle selection change
        select.listen('MDCSelect:change', () => {
            // The HTMX request will be triggered by the list item click
            console.log('Per page changed to:', select.value);
        });
    }

    // Initialize MDC Icon Buttons for pagination
    const paginationButtons = document.querySelectorAll('.mdc-data-table__pagination-button');
    if (paginationButtons.length > 0 && typeof mdc !== 'undefined' && mdc.iconButton) {
        paginationButtons.forEach(button => {
            new mdc.iconButton.MDCIconButtonToggle(button);
        });
    }

    // Initialize MDC Buttons for page numbers
    const pageButtons = document.querySelectorAll('.mdc-data-table__pagination-page-button');
    if (pageButtons.length > 0 && typeof mdc !== 'undefined' && mdc.ripple) {
        pageButtons.forEach(button => {
            new mdc.ripple.MDCRipple(button);
        });
    }
});
</script>
{% endif %}
